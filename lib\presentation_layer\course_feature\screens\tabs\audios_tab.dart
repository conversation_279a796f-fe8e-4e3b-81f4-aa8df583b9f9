import 'package:arepsalin/data_layer/model/week_subject_model.dart';
import 'package:flutter/material.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../../../constants/app_colors.dart';
import '../../../../translation/locale_keys.g.dart';
import '../../../../managers/theme_manager.dart';
import '../../widgets/general_item.dart';
import 'single_audio_screen.dart';

class AudiosTab extends StatefulWidget {
  const AudiosTab({
    super.key,
    required this.weekNo,
    required this.semesterNo,
    required this.subject,
  });

  final int weekNo;
  final int semesterNo;
  final WeekSubject subject;

  @override
  State<AudiosTab> createState() => _AudiosTabState();
}

class _AudiosTabState extends State<AudiosTab> {
  @override
  Widget build(BuildContext context) {
    // Filter items where itemType is audio from all lessons
    final audioItems = widget.subject.lessons
        .expand((lesson) => lesson.items)
        .where((item) => item.itemType == "audio")
        .toList();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (audioItems.isEmpty)
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(height: 40),
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: AppColors.warmBlue.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.headphones_outlined,
                      size: 40,
                      color: AppColors.warmBlue.withValues(alpha: 0.8),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    LocaleKeys.noAudioAvailable.tr(),
                    style: ThemeManager.medium(
                      size: 16,
                      color: AppColors.black.withValues(alpha: 0.7),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    LocaleKeys.audioLecturesWillBeAvailable.tr(),
                    style: ThemeManager.regular(
                      size: 14,
                      color: AppColors.grey,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            )
          else ...[
            // Header section
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.warmBlue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.headphones,
                      color: AppColors.warmBlue,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          LocaleKeys.audioLectures.tr(),
                          style: ThemeManager.semiBold(
                            size: 18,
                            color: AppColors.black,
                          ),
                        ),
                        Text(
                          '${audioItems.length} ${audioItems.length == 1 ? LocaleKeys.audioAvailable.tr() : LocaleKeys.audiosAvailable.tr()}',
                          style: ThemeManager.regular(
                            size: 14,
                            color: AppColors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            // Audio list
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: audioItems.length,
              itemBuilder: (context, index) => GeneralItem(
                onTabBackFunction: () {
                  PersistentNavBarNavigator.pushNewScreen(
                    context,
                    screen: SingleAudioScreen(
                      title: audioItems[index].title,
                      audio: audioItems[index].itemContent,
                    ),
                    withNavBar: true,
                    pageTransitionAnimation: PageTransitionAnimation.cupertino,
                  );
                },
                title: audioItems[index].title,
                subtitle: LocaleKeys.audioXofY.tr().replaceFirst('{}', '${index + 1}').replaceFirst('{}', '${audioItems.length}'),
                icon: Icons.play_circle_filled,
                iconColor: AppColors.white,
                iconBackgroundColor: AppColors.warmBlue,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

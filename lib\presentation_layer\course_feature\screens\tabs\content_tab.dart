import 'package:arepsalin/data_layer/model/week_subject_model.dart';
import 'package:flutter/material.dart';
import 'package:arepsalin/presentation_layer/course_feature/widgets/general_item.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../../../constants/app_colors.dart';
import '../../../../managers/theme_manager.dart';
import '../../../../translation/locale_keys.g.dart';
import '../pdf_screen.dart';

class ContentTab extends StatefulWidget {
  const ContentTab({
    super.key,
    required this.semesterNo,
    required this.weekNo,
    required this.subject,
  });

  final int weekNo;
  final int semesterNo;
  final WeekSubject subject;

  @override
  State<ContentTab> createState() => _ContentTabState();
}

class _ContentTabState extends State<ContentTab> {
  @override
  Widget build(BuildContext context) {
    // Filter items where itemType is pdf from all lessons
    final pdfItems = widget.subject.lessons
        .expand((lesson) => lesson.items)
        .where((item) => item.itemType == "pdf")
        .toList();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (pdfItems.isEmpty)
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(height: 40),
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: AppColors.bermuda.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.description_outlined,
                      size: 40,
                      color: AppColors.bermuda.withValues(alpha: 0.8),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    LocaleKeys.noContentAvailable.tr(),
                    style: ThemeManager.medium(
                      size: 16,
                      color: AppColors.black.withValues(alpha: 0.7),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    LocaleKeys.documentsWillAppear.tr(),
                    style: ThemeManager.regular(
                      size: 14,
                      color: AppColors.grey,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            )
          else ...[
            // Header section
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.bermuda.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.description,
                      color: AppColors.bermuda,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          LocaleKeys.courseMaterials.tr(),
                          style: ThemeManager.semiBold(
                            size: 18,
                            color: AppColors.black,
                          ),
                        ),
                        Text(
                          '${pdfItems.length} ${pdfItems.length == 1 ? LocaleKeys.documentAvailable.tr() : LocaleKeys.documentsAvailable.tr()}',
                          style: ThemeManager.regular(
                            size: 14,
                            color: AppColors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            // PDF list
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: pdfItems.length,
              itemBuilder: (context, index) => GeneralItem(
                onTabBackFunction: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => PdfScreen(
                        title: pdfItems[index].title,
                        pdf: pdfItems[index].itemContent,
                      ),
                    ),
                  );
                },
                title: pdfItems[index].title,
                subtitle: LocaleKeys.documentXofY.tr().replaceFirst('{}', '${index + 1}').replaceFirst('{}', '${pdfItems.length}'),
                icon: Icons.picture_as_pdf,
                iconColor: AppColors.white,
                iconBackgroundColor: AppColors.bermuda,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:arepsalin/constants/app_colors.dart';
import 'package:arepsalin/data_layer/model/week_quiz_model.dart';
import 'package:arepsalin/data_layer/service/quiz_service.dart';
import 'package:arepsalin/managers/theme_manager.dart';
import 'package:arepsalin/presentation_layer/course_feature/screens/assignment_screen.dart';
import 'package:arepsalin/presentation_layer/course_feature/screens/quiz/new_start_quiz_screen.dart';
import 'package:arepsalin/translation/locale_keys.g.dart';

class NewTestsTab extends StatefulWidget {
  const NewTestsTab({
    Key? key,
    required this.lessonId,
  }) : super(key: key);

  final int lessonId;

  @override
  State<NewTestsTab> createState() => _NewTestsTabState();
}

class _NewTestsTabState extends State<NewTestsTab> {
  bool _isLoading = true;
  List<WeekQuiz> _quizzes = [];
  String? _error;
  final QuizService _quizService = QuizService();

  @override
  void initState() {
    super.initState();
    _loadQuizzes();
  }

  Future<void> _loadQuizzes() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final quizzes = await _quizService.getLessonQuizzes(widget.lessonId);
      setState(() {
        _quizzes = quizzes;
        _isLoading = false;
      });
      debugPrint('Loaded ${quizzes.length} quizzes for lesson ${widget.lessonId}');
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
      debugPrint('Error loading quizzes: $e');
    }
  }

  @override
  void didUpdateWidget(NewTestsTab oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.lessonId != widget.lessonId) {
      _loadQuizzes();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(20.0),
          child: CircularProgressIndicator(
            color: AppColors.primary,
          ),
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                color: Colors.red.shade400,
                size: 48,
              ),
              const SizedBox(height: 16),
              Text(
                'Failed to load quizzes',
                style: ThemeManager.semiBold(
                  size: 16,
                  color: AppColors.black,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                _error!.replaceAll('Exception: ', ''),
                style: ThemeManager.regular(
                  size: 14,
                  color: AppColors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: _loadQuizzes,
                icon: const Icon(Icons.refresh),
                label: Text(LocaleKeys.tryAgain.tr()),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: AppColors.white,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_quizzes.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.quiz_outlined,
                color: AppColors.primary,
                size: 48,
              ),
              const SizedBox(height: 16),
              Text(
                LocaleKeys.noQuizzesAvailable.tr(),
                style: ThemeManager.semiBold(size: 16, color: AppColors.black),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'No quizzes are available for this lesson yet.',
                style: ThemeManager.regular(size: 14, color: AppColors.grey),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: AppColors.warmWhite,
        border: Border.all(
          color: AppColors.grey.withValues(alpha: 0.5),
          width: 1,
        ),
      ),
      child: ListView.builder(
        padding: const EdgeInsets.all(8),
        itemCount: _quizzes.length,
        itemBuilder: (context, index) {
          final quiz = _quizzes[index];
          return QuizCard(quiz: quiz);
        },
      ),
    );
  }
}

class QuizCard extends StatelessWidget {
  final WeekQuiz quiz;

  const QuizCard({Key? key, required this.quiz}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bool isDisabled = !quiz.isAccessible;
    final Color themeColor = quiz.statusColor;

    return Container(
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: isDisabled ? Colors.grey.shade100 : Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: isDisabled
              ? Colors.grey.withValues(alpha: 0.5)
              : themeColor.withValues(alpha: 0.7),
          width: 1.5,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: isDisabled
              ? null
              : () {
                  PersistentNavBarNavigator.pushNewScreen(
                    context,
                    screen: NewStartQuizScreen(quiz: quiz),
                    withNavBar: false,
                    pageTransitionAnimation: PageTransitionAnimation.cupertino,
                  );
                },
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        quiz.name,
                        style: ThemeManager.semiBold(
                          size: 16,
                          color: isDisabled ? Colors.grey : AppColors.black,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: themeColor.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        quiz.status.toUpperCase(),
                        style: ThemeManager.medium(
                          size: 12,
                          color: themeColor,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    // Conditionally show timer or record icon based on quiz type
                    if (quiz.isRecord== true) ...[
                      Icon(
                        Icons.mic,
                        size: 16,
                        color: isDisabled ? Colors.grey : themeColor,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'تسجيل صوتي',
                        style: ThemeManager.regular(
                          size: 14,
                          color: isDisabled ? Colors.grey : AppColors.black,
                        ),
                      ),
                    ] else if (quiz.timeLimit != null) ...[
                      Icon(
                        Icons.timer,
                        size: 16,
                        color: isDisabled ? Colors.grey : themeColor,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${quiz.timeLimit} ${LocaleKeys.minutes.tr()}',
                        style: ThemeManager.regular(
                          size: 14,
                          color: isDisabled ? Colors.grey : AppColors.black,
                        ),
                      ),
                    ] else ...[
                      Icon(
                        Icons.timer_off,
                        size: 16,
                        color: isDisabled ? Colors.grey : themeColor,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'بدون وقت محدد',
                        style: ThemeManager.regular(
                          size: 14,
                          color: isDisabled ? Colors.grey : AppColors.black,
                        ),
                      ),
                    ],
                    const SizedBox(width: 16),
                    Icon(
                      Icons.grade,
                      size: 16,
                      color: isDisabled ? Colors.grey : themeColor,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${quiz.grade} ${LocaleKeys.points.tr()}',
                      style: ThemeManager.regular(
                        size: 14,
                        color: isDisabled ? Colors.grey : AppColors.black,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.replay,
                      size: 16,
                      color: isDisabled ? Colors.grey : themeColor,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${quiz.userAttempts}/${quiz.numberOfAttempts} ${LocaleKeys.attempts.tr()}',
                      style: ThemeManager.regular(
                        size: 14,
                        color: isDisabled ? Colors.grey : AppColors.black,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
